/* TechLMS Admin Styles */
:root {
    --primary: #07FABA;
    --primary-dark: #06e5a8;
    --secondary: #2563eb;
    --secondary-dark: #1d4ed8;
    --success: #22c55e;
    --danger: #ef4444;
    --warning: #f59e0b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

.techlms-settings {
    max-width: 1000px;
    margin: 40px auto;
    padding: 0 20px;
}

.techlms-settings h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 1rem;
}

.techlms-settings .description {
    font-size: 1.1rem;
    color: var(--gray-600);
    margin-bottom: 2.5rem;
}

.course-summary {
    background: white;
    border-radius: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.course-summary > summary {
    list-style: none; /* Remove o marker padrão */
    position: relative; /* Para posicionar a seta */
    cursor: pointer;
}

/* Adiciona seta customizada */
.course-summary > summary::after {
    content: "⌵";
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%) rotate(0deg);
    font-size: 1.5rem;
    color: var(--gray-500);
    transition: transform 0.3s ease;
}

/* Rotaciona a seta quando aberto */
.course-summary[open] > summary::after {
    transform: translateY(-50%) rotate(180deg);
}

/* Remove o marker do Firefox */
.course-summary > summary::-webkit-details-marker {
    display: none;
}

/* Remove o marker do outros navegadores */
.course-summary > summary::marker {
    display: none;
}

.course-summary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.course-header {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1.5rem;
}

.course-thumbnail {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 0 0 3px var(--gray-200);
    object-fit: cover;
}

.course-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-800);
    margin: 0;
}

.course-settings {
    padding: 0 1.5rem 1.5rem;
}

.settings-section {
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    font-size: 1.2rem;
    color: var(--gray-700);
    margin: 0 0 1.5rem;
}

.setting-group {
    margin-bottom: 1.25rem;
}

.setting-group:last-child {
    margin-bottom: 0;
}

.setting-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--gray-700);
}

.setting-group input[type="text"],
.setting-group input[type="number"],
.setting-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.setting-group input[type="text"]:focus,
.setting-group input[type="number"]:focus,
.setting-group select:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(7, 250, 186, 0.2);
}

.submit-group {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
}

.button-primary {
    background: var(--secondary) !important;
    color: white !important;
    border: none !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.375rem !important;
    font-weight: 500 !important;
    cursor: pointer;
    transition: all 0.2s ease !important;
}

.button-primary:hover {
    background: var(--secondary-dark) !important;
    transform: translateY(-1px);
}