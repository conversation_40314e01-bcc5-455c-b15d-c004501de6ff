<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

use Elementor\Widget_Base;
use Elementor\Controls_Manager;

class Custom_Carousel_Meus_Cursos_Widget extends Widget_Base {

    public function get_name() {
        return 'custom_carousel_meus_cursos';
    }

    public function get_title() {
        return __( 'Carrossel Meus Cursos TECHFLIX', 'plugin-name' );
    }

    public function get_icon() {
        return 'eicon-post-list';
    }

    public function get_categories() {
        return [ 'custom_category' ];
    }

    protected function register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => __( 'Configurações do Carrossel', 'plugin-name' ),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        // Filtro de cursos por inclusão/exclusão, agora atualizado com base em _show_in_meus_cursos
        $this->add_control(
            'included_courses',
            [
                'label' => __( 'Incluir Cursos', 'plugin-name' ),
                'type' => Controls_Manager::SELECT2,
                'options' => $this->get_tutor_courses_filtered(),
                'multiple' => true,
                'label_block' => true,
            ]
        );

        $this->add_control(
            'excluded_courses',
            [
                'label' => __( 'Excluir Cursos', 'plugin-name' ),
                'type' => Controls_Manager::SELECT2,
                'options' => $this->get_tutor_courses_filtered(),
                'multiple' => true,
                'label_block' => true,
            ]
        );

        // Ordenação
        $this->add_control(
            'order_by',
            [
                'label' => __( 'Ordenar Por', 'plugin-name' ),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    'date' => __( 'Data de Inscrição', 'plugin-name' ),
                    'title' => __( 'Título', 'plugin-name' ),
                ],
                'default' => 'date',
            ]
        );

        $this->add_control(
            'order',
            [
                'label' => __( 'Ordem', 'plugin-name' ),
                'type' => Controls_Manager::SELECT,
                'options' => [
                    'asc' => __( 'Ascendente', 'plugin-name' ),
                    'desc' => __( 'Descendente', 'plugin-name' ),
                ],
                'default' => 'desc',
            ]
        );

        $this->end_controls_section();
        
        $this->end_controls_section();
     // Slides per view control
    // Seção de controles para Slides
        $this->start_controls_section(
        'section_slides',
        [
            'label' => __('Slides', 'plugin-name'),
            'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
        ]
    );

    // Slides per view options
    $slides_per_view_options = [
        1 => '1',
        2 => '2',
        3 => '3',
        4 => '4',
        5 => '5',
        6 => '6',
        7 => '7',
        8 => '8',
    ];

    // Slides per view control
    $this->add_responsive_control(
        'slides_per_view',
        [
            'label' => __('Slides Per View', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::SELECT,
            'devices' => ['widescreen', 'desktop', 'laptop', 'tablet_extra', 'tablet', 'mobile_extra', 'mobile'],
            'options' => ['' => esc_html__('Default', 'plugin-name')] + $slides_per_view_options,
            'default' => '',
            'frontend_available' => true,
            'widescreen_default' => 6,
            'desktop_default' => 5,
            'laptop_default' => 4,
            'tablet_extra_default' => 3,
            'tablet_default' => 2,
            'mobile_extra_default' => 2,
            'mobile_default' => 1,
        ]
    );
    
    // Space between slides control
    $this->add_responsive_control(
        'space_between',
        [
            'label' => __('Space Between Slides', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::SLIDER,
            'devices' => ['widescreen', 'desktop', 'laptop', 'tablet_extra', 'tablet', 'mobile_extra', 'mobile'],
            'size_units' => ['px', '%'],
            'range' => [
                'px' => [
                    'min' => 0,
                    'max' => 100,
                    'step' => 5,
                ],
                '%' => [
                    'min' => 0,
                    'max' => 100,
                    'step' => 1,
                ],
            ],
            'default' => [
                'unit' => 'px',
                'size' => 10,
            ],
            'frontend_available' => true,
            'widescreen_default' => [
                'unit' => 'px',
                'size' => 25,
            ],
            'desktop_default' => [
                'unit' => 'px',
                'size' => 20,
            ],
            'laptop_default' => [
                'unit' => 'px',
                'size' => 15,
            ],
            'tablet_extra_default' => [
                'unit' => 'px',
                'size' => 15,
            ],
            'tablet_default' => [
                'unit' => 'px',
                'size' => 10,
            ],
            'mobile_extra_default' => [
                'unit' => 'px',
                'size' => 10,
            ],
            'mobile_default' => [
                'unit' => 'px',
                'size' => 8,
            ],
        ]
    );

    $this->end_controls_section();

    // Seção de controles para Navegação
    $this->start_controls_section(
        'section_navigation',
        [
            'label' => __('Navegação', 'plugin-name'),
            'tab' => \Elementor\Controls_Manager::TAB_STYLE,
        ]
    );

    // Mostrar/Esconder Setas
    $this->add_control(
        'show_arrows',
        [
            'label' => __('Mostrar Setas', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::SWITCHER,
            'label_on' => __('Sim', 'plugin-name'),
            'label_off' => __('Não', 'plugin-name'),
            'default' => 'yes',
            'frontend_available' => true,
        ]
    );

    // Tamanho das Setas
    $this->add_responsive_control(
        'arrows_size',
        [
            'label' => __('Tamanho das Setas', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::SLIDER,
            'size_units' => ['px'],
            'range' => [
                'px' => [
                    'min' => 20,
                    'max' => 100,
                    'step' => 1,
                ],
            ],
            'devices' => ['widescreen', 'desktop', 'laptop', 'tablet_extra', 'tablet', 'mobile_extra', 'mobile'],
            'default' => [
                'unit' => 'px',
                'size' => 40,
            ],
            'selectors' => [
                '{{WRAPPER}} .swiper-button-prev, 
                 {{WRAPPER}} .swiper-button-next' => 'width: {{SIZE}}{{UNIT}} !important; height: {{SIZE}}{{UNIT}} !important;',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );

// Tamanho do Ícone das Setas
    $this->add_responsive_control(
        'arrows_icon_size',
        [
            'label' => __('Tamanho do Ícone', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::SLIDER,
            'size_units' => ['px'],
            'range' => [
                'px' => [
                    'min' => 10,
                    'max' => 50,
                    'step' => 1,
                ],
            ],
            'devices' => ['widescreen', 'desktop', 'laptop', 'tablet_extra', 'tablet', 'mobile_extra', 'mobile'],
            'default' => [
                'unit' => 'px',
                'size' => 20,
            ],
            'selectors' => [
                '{{WRAPPER}} .swiper-button-prev:after, 
                 {{WRAPPER}} .swiper-button-next:after' => 'font-size: {{SIZE}}{{UNIT}} !important;',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );

    // Padding Interno das Setas
    $this->add_responsive_control(
        'arrows_padding',
        [
            'label' => __('Padding Interno', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::DIMENSIONS,
            'size_units' => ['px', 'em', '%'],
            'devices' => ['widescreen', 'desktop', 'laptop', 'tablet_extra', 'tablet', 'mobile_extra', 'mobile'],
            'default' => [
                'top' => '10',
                'right' => '10',
                'bottom' => '10',
                'left' => '10',
                'unit' => 'px',
                'isLinked' => true,
            ],
            'selectors' => [
                '{{WRAPPER}} .swiper-button-prev, 
                 {{WRAPPER}} .swiper-button-next' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}} !important;',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );

    // Posição Horizontal das Setas
    $this->add_responsive_control(
        'arrows_offset',
        [
            'label' => __('Deslocamento Horizontal', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::SLIDER,
            'size_units' => ['px', '%'],
            'range' => [
                'px' => [
                    'min' => -100,
                    'max' => 100,
                    'step' => 1,
                ],
                '%' => [
                    'min' => -50,
                    'max' => 50,
                    'step' => 1,
                ],
            ],
            'devices' => ['widescreen', 'desktop', 'laptop', 'tablet_extra', 'tablet', 'mobile_extra', 'mobile'],
            'default' => [
                'unit' => 'px',
                'size' => 0,
            ],
            'selectors' => [
                '{{WRAPPER}} .swiper-button-prev' => 'left: {{SIZE}}{{UNIT}} !important;',
                '{{WRAPPER}} .swiper-button-next' => 'right: {{SIZE}}{{UNIT}} !important;',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );

    // Cor de Fundo das Setas
    $this->add_control(
        'arrows_background',
        [
            'label' => __('Cor de Fundo', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::COLOR,
            'default' => 'rgba(0, 0, 0, 0.5)',
            'selectors' => [
                '{{WRAPPER}} .swiper-button-prev, 
                 {{WRAPPER}} .swiper-button-next' => 'background-color: {{VALUE}} !important;',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );

    // Cor das Setas
    $this->add_control(
        'arrows_color',
        [
            'label' => __('Cor das Setas', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::COLOR,
            'default' => '#ffffff',
            'selectors' => [
                '{{WRAPPER}} .swiper-button-prev:after, 
                 {{WRAPPER}} .swiper-button-next:after' => 'color: {{VALUE}} !important;',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );
    // Border Radius das Setas
    $this->add_control(
        'arrows_border_radius',
        [
            'label' => __('Border Radius', 'plugin-name'),
            'type' => \Elementor\Controls_Manager::DIMENSIONS,
            'size_units' => ['px', '%'],
            'default' => [
                'top' => '50',
                'right' => '50',
                'bottom' => '50',
                'left' => '50',
                'unit' => '%',
                'isLinked' => true,
            ],
            'selectors' => [
                '{{WRAPPER}} .custom-carousel-wrapper .swiper-button-prev, 
                 {{WRAPPER}} .custom-carousel-wrapper .swiper-button-next' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
            ],
            'condition' => [
                'show_arrows' => 'yes',
            ],
        ]
    );

    $this->end_controls_section();

        // Seção de mensagem sem cursos
        $this->start_controls_section(
            'no_courses_section',
            [
                'label' => __( 'Mensagem Sem Cursos', 'plugin-name' ),
                'tab' => Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'no_courses_message',
            [
                'label' => __( 'Mensagem', 'plugin-name' ),
                'type' => Controls_Manager::TEXTAREA,
                'default' => __( 'Você ainda não possui nenhum curso comprado.', 'plugin-name' ),
            ]
        );

        $this->add_control(
            'no_courses_icon',
            [
                'label' => __( 'Ícone', 'plugin-name' ),
                'type' => Controls_Manager::MEDIA,
                'default' => [
                    'url' => \Elementor\Utils::get_placeholder_image_src(),
                ],
            ]
        );

        $this->add_control(
            'no_courses_icon_size',
            [
                'label' => __( 'Tamanho do Ícone', 'plugin-name' ),
                'type' => Controls_Manager::SLIDER,
                'size_units' => [ 'px', '%', 'em', 'rem', 'vw' ],
                'range' => [
                    'px' => [
                        'min' => 10,
                        'max' => 300,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                    'em' => [
                        'min' => 1,
                        'max' => 10,
                    ],
                    'rem' => [
                        'min' => 1,
                        'max' => 10,
                    ],
                    'vw' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .no-courses-message img' => 'width: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'no_courses_alignment',
            [
                'label' => __( 'Alinhamento', 'plugin-name' ),
                'type' => Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __( 'Esquerda', 'plugin-name' ),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __( 'Centro', 'plugin-name' ),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __( 'Direita', 'plugin-name' ),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'center',
                'toggle' => true,
            ]
        );

        $this->end_controls_section();
    }

protected function get_tutor_courses_filtered() {
    $args = [
        'post_type' => 'courses',
        'posts_per_page' => -1,
        'meta_query' => [
            [
                'key' => '_show_in_meus_cursos',
                'value' => 'yes',
                'compare' => '=',
            ],
        ],
    ];
    $query = new WP_Query($args);
    $courses = [];

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $courses[get_the_ID()] = get_the_title();
        }
        wp_reset_postdata();
    }

    return $courses;
}

protected function get_first_lesson_url($course_id) {
    return tutor_utils()->get_course_first_lesson($course_id);
}

protected function get_purchase_link($course_id) {
    return get_post_meta($course_id, '_techlink_redirect_url', true);
}

protected function render() {
    $settings = $this->get_settings_for_display();
    $widget_id = $this->get_id();
    $current_user_id = get_current_user_id();
    $user_data = get_userdata($current_user_id);
    $registration_date = strtotime($user_data->user_registered);
    $enrolled_courses = tutils()->get_enrolled_courses_ids_by_user($current_user_id);
    $all_meus_cursos = $this->get_tutor_courses_filtered();
    $filtered_courses = array_intersect($enrolled_courses, array_keys($all_meus_cursos));

// Adiciona valores padrão para todas as configurações necessárias
    $defaults = [
        'arrows_background' => 'rgba(0, 0, 0, 0.5)',
        'arrows_padding' => [
            'top' => '10',
            'right' => '10',
            'bottom' => '10',
            'left' => '10',
            'unit' => 'px',
            'isLinked' => true
        ],
        'arrows_border_radius' => [
            'top' => '50',
            'right' => '50',
            'bottom' => '50',
            'left' => '50',
            'unit' => '%',
            'isLinked' => true
        ],
        'arrows_color' => '#ffffff',
        'arrows_size' => [
            'size' => '40',
            'unit' => 'px'
        ],
        'arrows_icon_size' => [
            'size' => '20',
            'unit' => 'px'
        ],
        'arrows_offset' => [
            'size' => '0',
            'unit' => 'px'
        ],
        'show_arrows' => 'yes'
    ];

    // Combina as configurações do usuário com os valores padrão
    $settings = array_merge($defaults, $settings);
    
    // Apply inclusion and exclusion filters
    if (!empty($settings['included_courses'])) {
        $filtered_courses = array_intersect($filtered_courses, $settings['included_courses']);
    }

    if (!empty($settings['excluded_courses'])) {
        $filtered_courses = array_diff($filtered_courses, $settings['excluded_courses']);
    }

    // Sorting courses
    if ($settings['order_by'] === 'title') {
        usort($filtered_courses, function($a, $b) {
            return strcmp(get_the_title($a), get_the_title($b));
        });
    }

    if ($settings['order'] === 'desc') {
        $filtered_courses = array_reverse($filtered_courses);
    }

    if (!empty($filtered_courses)) {
        // Wrapper principal com classe para o Swiper
        echo '<div class="carousel-meus-cursos-wrapper" data-widget-id="' . esc_attr($widget_id) . '">';
        
        // Container do Swiper
        echo '<div class="swiper" id="swiper-' . esc_attr($widget_id) . '">';
        
        // Wrapper dos slides
        echo '<div class="swiper-wrapper">';

        foreach ($filtered_courses as $course_id) {
            $course_image = get_the_post_thumbnail_url($course_id, 'full');
            $custom_url = get_post_meta($course_id, '_techlink_custom_url', true);
            $course_link = !empty($custom_url) ? $custom_url : $this->get_first_lesson_url($course_id);
            $enable_comments = get_post_meta($course_id, '_enable_comments', true);
            
            // Verificar status do bônus
            $bonus_enabled = get_post_meta($course_id, '_bonus_enabled', true) === 'yes';
            $hourglass_icon = false;

            // Detectar orientação da imagem
            $course_image_id = get_post_thumbnail_id($course_id);
            $orientation_class = 'horizontal'; // default
            if ($course_image_id) {
                $image_data = wp_get_attachment_image_src($course_image_id, 'full');
                if ($image_data) {
                    $width = $image_data[1];
                    $height = $image_data[2];
                    $orientation_class = $height > $width ? 'vertical' : 'horizontal';
                }
            }

            if ($bonus_enabled) {
                $linked_course_id = get_post_meta($course_id, '_linked_course', true);
                $release_days = intval(get_post_meta($course_id, '_release_days', true));
                $release_date = strtotime("+$release_days days", $registration_date);
                $current_date = time();

                if (!tutils()->is_enrolled($linked_course_id, $current_user_id) || $current_date < $release_date) {
                    $hourglass_icon = true;
                    $course_link = '#';
                    $release_date_formatted = date('d/m/Y', $release_date);
                    $user_name = $user_data->first_name ? $user_data->first_name : $user_data->display_name;
                    $escaped_user_name = addslashes($user_name);
                    ?>
                    <script>
                        jQuery(document).ready(function($) {
                            $('#meus-course-link-<?php echo esc_js($course_id); ?>').data({
                                'modal-user-name': '<?php echo esc_js($escaped_user_name); ?>',
                                'modal-release-date': '<?php echo esc_js($release_date_formatted); ?>'
                            });
                        });
                    </script>
                    <?php
                }
            }

            // Estrutura do slide
            echo '<div class="swiper-slide">';
            echo '<div class="carousel-item ' . esc_attr($orientation_class) . '">';
            echo '<a id="meus-course-link-' . esc_attr($course_id) . '" 
                    href="' . esc_url($course_link) . '" 
                    class="course-link ' . ($hourglass_icon ? 'trigger-modal' : 'trigger-carousel') . '" 
                    data-enable-comments="' . esc_attr($enable_comments) . '" 
                    data-course-id="' . esc_attr($course_id) . '">';
            
            echo '<img src="' . esc_url($course_image) . '" alt="Imagem do curso">';
            
            if ($hourglass_icon) {
                echo '<span class="hourglass-icon">
                        <i class="fas fa-hourglass-half icon-color"></i>
                      </span>';
            }

            echo '</a>';
            echo '</div>'; // Fechamento do .carousel-item
            echo '</div>'; // Fechamento do .swiper-slide
        }
        
        
        echo '</div>'; // Fim do .swiper-wrapper
        
        echo '<div class="swiper-pagination"></div>';
        
        // Adiciona navegação
        if ($settings['show_arrows'] === 'yes') {
            echo '<div class="swiper-button-prev swiper-button-prev-' . esc_attr($widget_id) . '"></div>';
            echo '<div class="swiper-button-next swiper-button-next-' . esc_attr($widget_id) . '"></div>';
        }
        
        echo '</div>'; // Fim do .swiper
        echo '</div>'; // Fim do .carousel-meus-cursos-wrapper

        
            // Configurações do Swiper
$breakpoints = [
        1921 => [ // widescreen
            'slidesPerView' => $settings['slides_per_view_widescreen'] ?? 6,
            'spaceBetween' => $settings['space_between_widescreen']['size'] ?? 20,
        ],
        1440 => [ // desktop
            'slidesPerView' => $settings['slides_per_view_desktop'] ?? 6,
            'spaceBetween' => $settings['space_between_desktop']['size'] ?? 15,
        ],
        1024 => [ // laptop
            'slidesPerView' => $settings['slides_per_view_laptop'] ?? 4,
            'spaceBetween' => $settings['space_between_laptop']['size'] ?? 10,
        ],
        1200 => [ // tablet_extra
            'slidesPerView' => $settings['slides_per_view_tablet_extra'] ?? 3,
            'spaceBetween' => $settings['space_between_tablet_extra']['size'] ?? 10,
        ],
        768 => [ // tablet
            'slidesPerView' => $settings['slides_per_view_tablet'] ?? 2,
            'spaceBetween' => $settings['space_between_tablet']['size'] ?? 10,
        ],
        576 => [ // mobile_extra
            'slidesPerView' => $settings['slides_per_view_mobile_extra'] ?? 2,
            'spaceBetween' => $settings['space_between_mobile_extra']['size'] ?? 10,
        ],
        320 => [ // mobile
            'slidesPerView' => $settings['slides_per_view_mobile'] ?? 1,
            'spaceBetween' => $settings['space_between_mobile']['size'] ?? 8,
        ],
    ];
         ?>
<style>
    /* Container principal */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-meus-cursos-wrapper {
        padding: 0 10px;
    }

    /* Configurações base do Swiper */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper {
        overflow: visible;
    }

    /* Estilos base para o container do curso */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item {
        position: relative;
        width: 100%;
        height: auto;
        overflow: hidden;
        margin: 0 auto;
    }

    /* Estilos para imagens horizontais */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.horizontal {
        width: 100%;
        max-width: 500px;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.horizontal img {
        width: 100%;
        height: auto;
        display: block;
    }

    /* Estilos para imagens verticais */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.vertical {
        width: 300px;
        max-height: 600px;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.vertical img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    /* Estilos para o container do slide */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-slide {
        height: auto !important;
        display: flex;
        justify-content: center;
        margin-right: 10px;
    }

    /* Posicionamento dos ícones */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .lock-icon,
    .elementor-element-<?php echo esc_attr($widget_id); ?> .hourglass-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 2;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .icon-color {
        color: #ffffff;
        font-size: 20px;
    }

    /* Hover effects */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item a:hover .lock-icon,
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item a:hover .hourglass-icon {
        background-color: rgba(0, 0, 0, 0.7);
        transform: scale(1.1);
    }

    /* Container do link */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .course-link {
        display: block;
        position: relative;
        width: 100%;
    }

    /* Efeito grayscale */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.grayscale img {
        filter: grayscale(100%);
    }

    /* Estilos base para as setas de navegação */
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-prev,
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-next {
            padding: <?php 
                $padding = $settings['arrows_padding'] ?? [];
                echo esc_attr($padding['top'] ?? '10') . 'px ' . 
                     esc_attr($padding['right'] ?? '10') . 'px ' . 
                     esc_attr($padding['bottom'] ?? '10') . 'px ' . 
                     esc_attr($padding['left'] ?? '10') . 'px';
            ?>;
            border-radius: <?php 
                $radius = $settings['arrows_border_radius'] ?? [];
                echo esc_attr($radius['top'] ?? '50') . ($radius['unit'] ?? '%') . ' ' . 
                     esc_attr($radius['right'] ?? '50') . ($radius['unit'] ?? '%') . ' ' . 
                     esc_attr($radius['bottom'] ?? '50') . ($radius['unit'] ?? '%') . ' ' . 
                     esc_attr($radius['left'] ?? '50') . ($radius['unit'] ?? '%');
            ?>;
            background-color: <?php echo esc_attr($settings['arrows_background']); ?>;
        }

    /* Estilos para os ícones das setas */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-prev:after,
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-next:after {
        font-size: <?php echo esc_attr($settings['arrows_icon_size']['size'] ?? '20'); ?>px;
        color: <?php echo esc_attr($settings['arrows_color'] ?: '#ffffff'); ?>;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }

    /* Posicionamento das setas */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-prev {
        left: <?php echo esc_attr($settings['arrows_offset']['size'] ?? '0'); ?><?php echo esc_attr($settings['arrows_offset']['unit'] ?? 'px'); ?>;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-next {
        right: <?php echo esc_attr($settings['arrows_offset']['size'] ?? '0'); ?><?php echo esc_attr($settings['arrows_offset']['unit'] ?? 'px'); ?>;
    }

    /* Estados das setas */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-disabled {
        opacity: 0;
        cursor: auto;
        pointer-events: none;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-hidden {
        display: none;
    }

    /* Estado de carregamento */
.elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-meus-cursos-wrapper.loading {
    position: relative;
    min-height: 400px;
}

.elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-meus-cursos-wrapper.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1;
}

.elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-meus-cursos-wrapper.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    z-index: 2;
    animation: carouselSpin 1s linear infinite;
}

@keyframes carouselSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Controle de visibilidade inicial */
.elementor-element-<?php echo esc_attr($widget_id); ?> .swiper {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

.elementor-element-<?php echo esc_attr($widget_id); ?> .swiper.swiper-initialized {
    opacity: 1;
    visibility: visible;
}

/* Estilos para a paginação */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination {
        position: relative;
        margin-top: 20px;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
        display: inline-block;
        border-radius: 50%;
        background: #2F2F2F;
        opacity: .2;
        transition: all .3s ease;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-bullet-active {
        opacity: 1;
        background: #868686;
        transform: scale(1.2);
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-bullet-active-main {
        transform: scale(1.4);
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-bullet-active-prev,
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-bullet-active-next {
        transform: scale(1.1);
    }

    /* Ajuste de espaçamento para bullets dinâmicos */
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-horizontal > .swiper-pagination-bullets,
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-bullets.swiper-pagination-horizontal {
        bottom: -30px;
    }

    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
    .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
        margin: 0 4px;
    }

    /* Responsividade */
    /* Tablet */
    @media (max-width: 1024px) {
        .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.vertical {
            width: 200px;
            max-height: 400px;
        }
        
        .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.horizontal {
            max-width: 300px;
        }
        
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-slide {
            margin-right: 8px;
        }
    }

    /* Mobile Extra */
    @media (max-width: 880px) {
        .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.vertical {
            width: 200px;
            max-height: 400px;
        }
        
        .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.horizontal {
            max-width: 300px;
        }
        
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-slide {
            margin-right: 6px;
        }
    }

    /* Mobile */
    @media (max-width: 767px) {
        .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.vertical {
            width: 200px;
            max-height: 400px;
        }
        
        .elementor-element-<?php echo esc_attr($widget_id); ?> .carousel-item.horizontal {
            max-width: 300px;
        }
        
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-slide {
            margin-right: 5px;
        }
    }

    <?php
    // Media queries para as setas
    foreach ($breakpoints as $device => $width) {
        if (isset($settings['arrows_size_' . $device])) {
            echo "@media (max-width: {$width}px) {
                .elementor-element-" . esc_attr($widget_id) . " .swiper-button-prev,
                .elementor-element-" . esc_attr($widget_id) . " .swiper-button-next {
                    width: " . esc_attr($settings['arrows_size_' . $device]['size']) . "px;
                    height: " . esc_attr($settings['arrows_size_' . $device]['size']) . "px;
                }
            }";
        }
    }
    ?>
</style>
<?php

        // Configurações do Swiper e Estilos
        $this->render_swiper_config($widget_id, $settings);
        $this->render_navigation_styles($widget_id, $settings);

    } else {
        $this->render_no_courses_message($settings);
    }
}

private function render_swiper_config($widget_id, $settings) {
    $breakpoints = [
        1921 => ['slidesPerView' => $settings['slides_per_view_widescreen'] ?? 6, 'spaceBetween' => $settings['space_between_widescreen']['size'] ?? 20],
        1440 => ['slidesPerView' => $settings['slides_per_view_desktop'] ?? 6, 'spaceBetween' => $settings['space_between_desktop']['size'] ?? 15],
        1024 => ['slidesPerView' => $settings['slides_per_view_laptop'] ?? 4, 'spaceBetween' => $settings['space_between_laptop']['size'] ?? 10],
        1200 => ['slidesPerView' => $settings['slides_per_view_tablet_extra'] ?? 3, 'spaceBetween' => $settings['space_between_tablet_extra']['size'] ?? 10],
        768 => ['slidesPerView' => $settings['slides_per_view_tablet'] ?? 2, 'spaceBetween' => $settings['space_between_tablet']['size'] ?? 10],
        576 => ['slidesPerView' => $settings['slides_per_view_mobile_extra'] ?? 2, 'spaceBetween' => $settings['space_between_mobile_extra']['size'] ?? 10],
        320 => ['slidesPerView' => $settings['slides_per_view_mobile'] ?? 1, 'spaceBetween' => $settings['space_between_mobile']['size'] ?? 8],
    ];
    ?>
<script>
jQuery(document).ready(function($) {
    // Esconde o carrossel enquanto carrega
    $('.elementor-element-<?php echo esc_js($widget_id); ?> .swiper').css('visibility', 'hidden');
    
    const initSwiper = () => {
        // Adiciona classe de loading
        $('.elementor-element-<?php echo esc_js($widget_id); ?> .custom-carousel-wrapper').addClass('loading');
        
        const swiperElement = $('#swiper-<?php echo esc_js($widget_id); ?>');
        const totalSlides = swiperElement.find('.swiper-slide').length;
        const mobileSlidesSetting = <?php echo esc_js($settings['slides_per_view_mobile'] ?? 1); ?>;
        
        const swiper = new Swiper('#swiper-<?php echo esc_js($widget_id); ?>', {
            slidesPerView: <?php echo esc_js($settings['slides_per_view_desktop'] ?? 6); ?>,
            spaceBetween: <?php echo esc_js($settings['space_between_desktop']['size'] ?? 20); ?>,
            observer: true,
            observeParents: true,
            init: false,
            breakpoints: {
                320: {
                    // Se houver apenas 1 slide, usa 'auto', senão usa a configuração do controle
                    slidesPerView: totalSlides === 1 ? 'auto' : mobileSlidesSetting,
                    spaceBetween: <?php echo esc_js($settings['space_between_mobile']['size'] ?? 8); ?>
                },
                576: {
                    slidesPerView: <?php echo esc_js($settings['slides_per_view_mobile_extra'] ?? 2); ?>,
                    spaceBetween: <?php echo esc_js($settings['space_between_mobile_extra']['size'] ?? 10); ?>
                },
                768: {
                    slidesPerView: <?php echo esc_js($settings['slides_per_view_tablet'] ?? 2); ?>,
                    spaceBetween: <?php echo esc_js($settings['space_between_tablet']['size'] ?? 10); ?>
                },
                1024: {
                    slidesPerView: <?php echo esc_js($settings['slides_per_view_laptop'] ?? 4); ?>,
                    spaceBetween: <?php echo esc_js($settings['space_between_laptop']['size'] ?? 10); ?>
                },
                1200: {
                    slidesPerView: <?php echo esc_js($settings['slides_per_view_tablet_extra'] ?? 3); ?>,
                    spaceBetween: <?php echo esc_js($settings['space_between_tablet_extra']['size'] ?? 10); ?>
                },
                1440: {
                    slidesPerView: <?php echo esc_js($settings['slides_per_view_desktop'] ?? 6); ?>,
                    spaceBetween: <?php echo esc_js($settings['space_between_desktop']['size'] ?? 15); ?>
                },
                1921: {
                    slidesPerView: <?php echo esc_js($settings['slides_per_view_widescreen'] ?? 6); ?>,
                    spaceBetween: <?php echo esc_js($settings['space_between_widescreen']['size'] ?? 20); ?>
                }
            },
            navigation: {
                nextEl: '.swiper-button-next-<?php echo esc_js($widget_id); ?>',
                prevEl: '.swiper-button-prev-<?php echo esc_js($widget_id); ?>'
            },
    pagination: {
        el: '.swiper-pagination',
        type: 'bullets',
        clickable: true,
        dynamicBullets: true,
        dynamicMainBullets: 3,
        // Controla a renderização dos bullets
        renderBullet: function (index, className) {
            // Se tiver apenas 1 slide e for mobile, não mostra a paginação
            if (totalSlides === 1 && window.innerWidth <= 576) {
                return '';
            }
            return '<span class="' + className + '"></span>';
        }
    },
    on: {
        init: function() {
            $('.elementor-element-<?php echo esc_js($widget_id); ?> .swiper').css('visibility', 'visible');
            $('.elementor-element-<?php echo esc_js($widget_id); ?> .custom-carousel-wrapper').removeClass('loading');
            
            // Verifica se deve ocultar a navegação e paginação
            const isMobile = window.innerWidth <= 576;
            if (totalSlides <= 1 && isMobile) {
                $('.swiper-button-next-<?php echo esc_js($widget_id); ?>, .swiper-button-prev-<?php echo esc_js($widget_id); ?>').hide();
                $('.elementor-element-<?php echo esc_js($widget_id); ?> .swiper-pagination').hide();
                $('.elementor-element-<?php echo esc_js($widget_id); ?> .swiper-slide').css({
                    'width': '100%',
                    'margin-right': '0'
                });
            }
        },
        resize: function() {
            const isMobile = window.innerWidth <= 576;
            if (totalSlides <= 1 && isMobile) {
                $('.swiper-button-next-<?php echo esc_js($widget_id); ?>, .swiper-button-prev-<?php echo esc_js($widget_id); ?>').hide();
                $('.elementor-element-<?php echo esc_js($widget_id); ?> .swiper-pagination').hide();
            } else {
                if (<?php echo esc_js($settings['show_arrows'] === 'yes' ? 'true' : 'false'); ?>) {
                    $('.swiper-button-next-<?php echo esc_js($widget_id); ?>, .swiper-button-prev-<?php echo esc_js($widget_id); ?>').show();
                }
                $('.elementor-element-<?php echo esc_js($widget_id); ?> .swiper-pagination').show();
            }
            this.update();
        }
    }
});
        // Garante que todas as imagens estejam carregadas antes de iniciar
        const images = $('.elementor-element-<?php echo esc_js($widget_id); ?> img');
        let loadedImages = 0;

        function tryInit() {
            loadedImages++;
            if (loadedImages === images.length) {
                swiper.init();
            }
        }

        if (images.length > 0) {
            images.each(function() {
                if (this.complete) {
                    tryInit();
                } else {
                    $(this).on('load', tryInit);
                }
            });
        } else {
            swiper.init();
        }

        return swiper;
    };

    const swiper = initSwiper();

    if (window.elementorFrontend?.isEditMode()) {
        elementor.channels.editor.on('change', function(view) {
            if (view.container.id === '<?php echo esc_js($widget_id); ?>') {
                setTimeout(() => {
                    swiper.update();
                }, 100);
            }
        });
    }
});
</script>
    <?php
}



private function render_navigation_styles($widget_id, $settings) {
    ?>
    <style>
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-prev,
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-next {
            background-color: <?php echo esc_attr($settings['arrows_background']); ?>;
            width: <?php echo esc_attr($settings['arrows_size']['size']); ?>px;
            height: <?php echo esc_attr($settings['arrows_size']['size']); ?>px;
        }

        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-prev:after,
        .elementor-element-<?php echo esc_attr($widget_id); ?> .swiper-button-next:after {
            color: <?php echo esc_attr($settings['arrows_color']); ?>;
            font-size: <?php echo esc_attr($settings['arrows_icon_size']['size']); ?>px;
        }
    </style>
    <?php
}

private function render_no_courses_message($settings) {
    $alignment = $settings['no_courses_alignment'] ? $settings['no_courses_alignment'] : 'center';
    ?>
    <div class="no-courses-message" style="text-align: <?php echo esc_attr($alignment); ?>;">
        <?php if (!empty($settings['no_courses_icon']['url'])) : ?>
            <img src="<?php echo esc_url($settings['no_courses_icon']['url']); ?>" alt="Ícone">
        <?php endif; ?>
        <p><?php echo esc_html($settings['no_courses_message']); ?></p>
    </div>
    <?php
}

protected function content_template() {}

}

function register_custom_carousel_meus_cursos_widget($widgets_manager) {
    $widgets_manager->register_widget_type(new Custom_Carousel_Meus_Cursos_Widget());
}
add_action('elementor/widgets/widgets_registered', 'register_custom_carousel_meus_cursos_widget');

function add_custom_carousel_meus_cursos_category($elements_manager) {
    $elements_manager->add_category(
        'custom_category',
        [
            'title' => __( 'TECHFLIX', 'plugin-name' ),
            'icon' => 'fa fa-plug',
            'position' => 3,
        ]
    );
}
add_action('elementor/elements/categories_registered', 'add_custom_carousel_meus_cursos_category');
